---
description: 
globs: 
alwaysApply: false
---
# 字典数据查询规则

## 核心原则
当需要字典项数据时，必须使用 `mcp_sqlite-explorer_read_query` 工具查询数据库。

## 常用查询模式

### 获取字典条目
```typescript
mcp_sqlite-explorer_read_query({
  query: `SELECT di.item_id, di.item_key, di.item_value 
          FROM dictionary_items di 
          JOIN dictionaries d ON di.dictionary_id = d.id 
          WHERE d.name = ? AND di.status = 'active'`,
  params: ["字典名称"]
})
```

### 验证字典项
```typescript
mcp_sqlite-explorer_read_query({
  query: `SELECT item_id, item_value FROM dictionary_items 
          WHERE item_id = ? AND status = 'active'`,
  params: [item_id]
})
```

## 注意事项
- 始终过滤 `status = 'active'` 确保数据有效
- 使用参数化查询避免 SQL 注入
- 常用字典：疾病类型、项目阶段、项目状态、人员角色、申办方、补贴类型
