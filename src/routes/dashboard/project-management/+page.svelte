<script lang="ts">
  import { onMount } from 'svelte';
  import { 
    Users, Activity, CheckCircle, Calendar, 
    DollarSign, UserCheck, TrendingUp, Clock, Target, BarChart3,
    Building, Stethoscope, AlertCircle
  } from 'lucide-svelte';

  // 导入图表组件
  import PieChart from '$lib/components/dashboard/charts/PieChart.svelte';
  import BarChart from '$lib/components/dashboard/charts/BarChart.svelte';
  import LineChart from '$lib/components/dashboard/charts/LineChart.svelte';
  import KpiCard from '$lib/components/dashboard/charts/KpiCard.svelte';
  import DataTable from '$lib/components/dashboard/charts/DataTable.svelte';
  import MetricCard from '$lib/components/dashboard/charts/MetricCard.svelte';

  // 导入服务和存储
  import { dashboardService } from '$lib/services/dashboardService';
  import type { FinancialMetrics, PersonnelMetrics, TimelineMetrics, ChartDataPoint } from '$lib/services/dashboardService';
  import {
    rawDashboardData,
    isLoading,
    errors,
    projectStatusChartOptions,
    projectStageChartOptions,
    recruitmentStatusChartOptions,
    diseaseChartOptions,
    monthlyNewProjectsChartOptions
  } from '$lib/stores/dashboardStores';
  import { goto } from '$app/navigation';

  // 状态
  let selectedView = 'overview'; // 'overview', 'financial', 'personnel', 'timeline', 'sponsor'

  // 新增数据存储
  let financialMetrics: FinancialMetrics = {
    totalSubsidyAmount: 0,
    averageSubsidyPerProject: 0,
    subsidyByType: [],
    topSubsidizedProjects: []
  };

  let personnelMetrics: PersonnelMetrics = {
    totalPersonnel: 0,
    personnelByRole: [],
    personnelUtilization: [],
    piDistribution: []
  };

  let timelineMetrics: TimelineMetrics = {
    projectsByStartMonth: [],
    averageProjectDuration: 0,
    upcomingDeadlines: [],
    projectPhaseTransitions: []
  };

  // 加载仪表盘概览数据
  async function loadDashboardOverview() {
    try {
      $isLoading.overview = true;
      $errors.overview = undefined;

      console.log('加载仪表盘概览数据');
      
      const overview = await dashboardService.getDashboardOverview();
      console.log('概览数据加载成功:', overview);
      
      $rawDashboardData = { ...$rawDashboardData, overview };
    } catch (err: any) {
      $errors.overview = err.message || '加载仪表盘概览数据失败';
      console.error('加载仪表盘概览数据失败:', err);
    } finally {
      $isLoading.overview = false;
    }
  }

  // 加载财务指标数据
  async function loadFinancialMetrics() {
    try {
      $isLoading.financial = true;
      $errors.financial = undefined;

      console.log('加载财务指标数据');
      
      const financial = await dashboardService.getFinancialMetrics();
      console.log('财务指标数据加载成功:', financial);
      financialMetrics = financial;
    } catch (err: any) {
      $errors.financial = err.message || '加载财务指标数据失败';
      console.error('加载财务指标数据失败:', err);
    } finally {
      $isLoading.financial = false;
    }
  }

  // 加载人员指标数据
  async function loadPersonnelMetrics() {
    try {
      $isLoading.personnel = true;
      $errors.personnel = undefined;

      console.log('加载人员指标数据');
      
      const personnel = await dashboardService.getPersonnelMetrics();
      console.log('人员指标数据加载成功:', personnel);
      personnelMetrics = personnel;
    } catch (err: any) {
      $errors.personnel = err.message || '加载人员指标数据失败';
      console.error('加载人员指标数据失败:', err);
    } finally {
      $isLoading.personnel = false;
    }
  }

  // 加载时间线指标数据
  async function loadTimelineMetrics() {
    try {
      $isLoading.timeline = true;
      $errors.timeline = undefined;

      console.log('加载时间线指标数据');
      
      const timeline = await dashboardService.getTimelineMetrics();
      console.log('时间线指标数据加载成功:', timeline);
      timelineMetrics = timeline;
    } catch (err: any) {
      $errors.timeline = err.message || '加载时间线指标数据失败';
      console.error('加载时间线指标数据失败:', err);
    } finally {
      $isLoading.timeline = false;
    }
  }

  // 加载项目状态分布数据
  async function loadProjectStatusDistribution() {
    try {
      $isLoading.projectStatus = true;
      $errors.projectStatus = undefined;

      console.log('加载项目状态分布数据');

      const distribution = await dashboardService.getProjectStatusDistribution();
      const chartData = await dashboardService.getProjectStatusChartData();

      console.log('项目状态分布数据加载成功:', { distribution, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        projectStatusDistribution: distribution,
        projectStatusChartData: chartData
      };
    } catch (err: any) {
      $errors.projectStatus = err.message || '加载项目状态分布数据失败';
      console.error('加载项目状态分布数据失败:', err);
    } finally {
      $isLoading.projectStatus = false;
    }
  }

  // 加载项目阶段分布数据
  async function loadProjectStageDistribution() {
    try {
      $isLoading.projectStage = true;
      $errors.projectStage = undefined;

      console.log('加载项目阶段分布数据');

      const distribution = await dashboardService.getProjectStageDistribution();
      const chartData = await dashboardService.getProjectStageChartData();

      console.log('项目阶段分布数据加载成功:', { distribution, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        projectStageDistribution: distribution,
        projectStageChartData: chartData
      };
    } catch (err: any) {
      $errors.projectStage = err.message || '加载项目阶段分布数据失败';
      console.error('加载项目阶段分布数据失败:', err);
    } finally {
      $isLoading.projectStage = false;
    }
  }

  // 加载招募状态分布数据
  async function loadRecruitmentStatusDistribution() {
    try {
      $isLoading.recruitmentStatus = true;
      $errors.recruitmentStatus = undefined;

      console.log('加载招募状态分布数据');

      const distribution = await dashboardService.getRecruitmentStatusDistribution();
      const chartData = await dashboardService.getRecruitmentStatusChartData();

      console.log('招募状态分布数据加载成功:', { distribution, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        recruitmentStatusDistribution: distribution,
        recruitmentStatusChartData: chartData
      };
    } catch (err: any) {
      $errors.recruitmentStatus = err.message || '加载招募状态分布数据失败';
      console.error('加载招募状态分布数据失败:', err);
    } finally {
      $isLoading.recruitmentStatus = false;
    }
  }

  // 加载疾病分布数据
  async function loadDiseaseDistribution() {
    try {
      $isLoading.disease = true;
      $errors.disease = undefined;

      console.log('加载疾病分布数据');

      const distribution = await dashboardService.getDiseaseDistribution();
      const chartData = await dashboardService.getDiseaseChartData();

      console.log('疾病分布数据加载成功:', { distribution, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        diseaseDistribution: distribution,
        diseaseChartData: chartData
      };
    } catch (err: any) {
      $errors.disease = err.message || '加载疾病分布数据失败';
      console.error('加载疾病分布数据失败:', err);
    } finally {
      $isLoading.disease = false;
    }
  }

  // 加载每月新增项目数据
  async function loadMonthlyNewProjects() {
    try {
      $isLoading.monthlyNewProjects = true;
      $errors.monthlyNewProjects = undefined;

      console.log('加载每月新增项目数据');

      const monthlyData = await dashboardService.getMonthlyNewProjects();
      const chartData = await dashboardService.getMonthlyNewProjectsChartData();

      console.log('每月新增项目数据加载成功:', { monthlyData, chartData });

      $rawDashboardData = {
        ...$rawDashboardData,
        monthlyNewProjects: monthlyData,
        monthlyNewProjectsChartData: chartData
      };
    } catch (err: any) {
      $errors.monthlyNewProjects = err.message || '加载每月新增项目数据失败';
      console.error('加载每月新增项目数据失败:', err);
    } finally {
      $isLoading.monthlyNewProjects = false;
    }
  }

  // 调试项目数据
  async function debugProjectData() {
    try {
      const result = await dashboardService.debugProjectDateStatistics();
      console.log('调试结果:', result);
      alert('调试信息已输出到控制台和日志，请检查开发者工具');
    } catch (err: any) {
      console.error('调试失败:', err);
      alert('调试失败: ' + err.message);
    }
  }

  // 切换视图
  function switchView(view: string) {
    selectedView = view;
  }

  // 导航到项目列表
  function navigateToProjects() {
    goto('/projects');
  }

  // 组件挂载时加载数据
  onMount(() => {
    loadDashboardOverview();
    loadProjectStatusDistribution();
    loadProjectStageDistribution();
    loadRecruitmentStatusDistribution();
    loadDiseaseDistribution();
    loadMonthlyNewProjects();
    
    // 加载新增数据
    loadFinancialMetrics();
    loadPersonnelMetrics();
    loadTimelineMetrics();
  });
</script>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="container mx-auto px-4 py-8">
    <!-- 标题和操作区域 -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">项目管理仪表盘</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">全方位项目运营数据分析与监控</p>
      </div>

      <div class="flex flex-wrap gap-2">
        <button
          type="button"
          onclick={() => navigateToProjects()}
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          查看项目列表
        </button>
      </div>
    </div>

    <!-- 视图切换标签 -->
    <div class="mb-8">
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          <button
            onclick={() => switchView('overview')}
            class="py-2 px-1 border-b-2 font-medium text-sm transition-colors {selectedView === 'overview' 
              ? 'border-blue-500 text-blue-600 dark:text-blue-400' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'}"
          >
            <Activity class="inline w-4 h-4 mr-2" />
            概览
          </button>
          
          <button
            onclick={() => switchView('financial')}
            class="py-2 px-1 border-b-2 font-medium text-sm transition-colors {selectedView === 'financial' 
              ? 'border-blue-500 text-blue-600 dark:text-blue-400' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'}"
          >
            <DollarSign class="inline w-4 h-4 mr-2" />
            财务指标
          </button>
          
          <button
            onclick={() => switchView('personnel')}
            class="py-2 px-1 border-b-2 font-medium text-sm transition-colors {selectedView === 'personnel' 
              ? 'border-blue-500 text-blue-600 dark:text-blue-400' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'}"
          >
            <UserCheck class="inline w-4 h-4 mr-2" />
            人员分析
          </button>
          
          <button
            onclick={() => switchView('timeline')}
            class="py-2 px-1 border-b-2 font-medium text-sm transition-colors {selectedView === 'timeline' 
              ? 'border-blue-500 text-blue-600 dark:text-blue-400' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'}"
          >
            <Clock class="inline w-4 h-4 mr-2" />
            时间线分析
          </button>
        </nav>
      </div>
    </div>

    <!-- 概览视图 -->
    {#if selectedView === 'overview'}
      <!-- 主要 KPI 指标 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {#key $rawDashboardData.overview?.active_project_count || 0}
          <KpiCard
            title="活动项目数"
            value={$rawDashboardData.overview?.active_project_count || 0}
            icon={Activity}
            color="blue"
            isLoading={$isLoading.overview}
            subtitle="正在进行的项目"
          />

          <KpiCard
            title="总项目数"
            value={$rawDashboardData.overview?.total_project_count || 0}
            icon={Target}
            color="indigo"
            isLoading={$isLoading.overview}
            subtitle="项目总数量"
          />

          <KpiCard
            title="招募中项目"
            value={$rawDashboardData.overview?.recruiting_project_count || 0}
            icon={Users}
            color="green"
            isLoading={$isLoading.overview}
            subtitle="正在招募患者"
          />

          <KpiCard
            title="本月新项目"
            value={$rawDashboardData.monthlyNewProjects && $rawDashboardData.monthlyNewProjects.length > 0
              ? $rawDashboardData.monthlyNewProjects[$rawDashboardData.monthlyNewProjects.length - 1].project_count
              : 0}
            icon={Calendar}
            color="purple"
            isLoading={$isLoading.monthlyNewProjects}
            subtitle="当月新启动"
          />
        {/key}
      </div>

      <!-- 核心图表 - 2x3 网格 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-8">
        {#key `${JSON.stringify($rawDashboardData.projectStatusChartData)}-${JSON.stringify($rawDashboardData.projectStageChartData)}-${JSON.stringify($rawDashboardData.diseaseChartData)}`}
          <!-- 项目状态分布 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">项目状态分布</h3>
              <BarChart3 class="w-5 h-5 text-gray-500" />
            </div>

            {#if $errors.projectStatus}
              <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
                <AlertCircle class="w-4 h-4 mr-2" />
                {$errors.projectStatus}
              </div>
            {/if}

            <PieChart
              options={$projectStatusChartOptions}
              height="300px"
            />
          </div>

          <!-- 项目阶段分布 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">项目阶段分布</h3>
              <Target class="w-5 h-5 text-gray-500" />
            </div>

            {#if $errors.projectStage}
              <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
                <AlertCircle class="w-4 h-4 mr-2" />
                {$errors.projectStage}
              </div>
            {/if}

            <PieChart
              options={$projectStageChartOptions}
              height="300px"
            />
          </div>

          <!-- 疾病领域分布 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">疾病领域分布</h3>
              <Stethoscope class="w-5 h-5 text-gray-500" />
            </div>

            {#if $errors.disease}
              <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
                <AlertCircle class="w-4 h-4 mr-2" />
                {$errors.disease}
              </div>
            {/if}

            <BarChart
              options={$diseaseChartOptions}
              height="300px"
            />
          </div>

          <!-- 招募状态分布 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">招募状态分布</h3>
              <CheckCircle class="w-5 h-5 text-gray-500" />
            </div>

            {#if $errors.recruitmentStatus}
              <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
                <AlertCircle class="w-4 h-4 mr-2" />
                {$errors.recruitmentStatus}
              </div>
            {/if}

            <PieChart
              options={$recruitmentStatusChartOptions}
              height="300px"
            />
          </div>

          <!-- 每月新项目趋势 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">新项目趋势</h3>
              <div class="flex items-center space-x-2">
                <button
                  onclick={debugProjectData}
                  class="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  title="调试项目数据"
                >
                  调试
                </button>
                <TrendingUp class="w-5 h-5 text-gray-500" />
              </div>
            </div>

            {#if $errors.monthlyNewProjects}
              <div class="flex items-center text-red-500 dark:text-red-400 text-sm mb-4">
                <AlertCircle class="w-4 h-4 mr-2" />
                {$errors.monthlyNewProjects}
              </div>
            {/if}

            {#if $isLoading.monthlyNewProjects}
              <div class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span class="ml-2 text-gray-600 dark:text-gray-400">加载趋势数据中...</span>
              </div>
            {:else if $rawDashboardData.monthlyNewProjects && $rawDashboardData.monthlyNewProjects.length === 0}
              <div class="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                <div class="text-center">
                  <Calendar class="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p class="text-lg font-medium mb-2">暂无月度项目数据</p>
                  <p class="text-sm">数据库中可能没有有效的项目启动日期</p>
                  <p class="text-xs mt-2">点击"调试"按钮查看详细信息</p>
                </div>
              </div>
            {:else if $rawDashboardData.monthlyNewProjectsChartData && $rawDashboardData.monthlyNewProjectsChartData.length > 0}
              <LineChart
                options={$monthlyNewProjectsChartOptions}
                height="300px"
              />
            {:else}
              <div class="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                <div class="text-center">
                  <TrendingUp class="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p class="text-lg font-medium mb-2">暂无图表数据</p>
                  <p class="text-sm">正在加载图表数据...</p>
                </div>
              </div>
            {/if}
          </div>
        {/key}
      </div>
    {/if}

    <!-- 财务指标视图 -->
    {#if selectedView === 'financial'}
      <!-- 财务 KPI -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <KpiCard
          title="总补助金额"
          value={financialMetrics.totalSubsidyAmount}
          icon={DollarSign}
          color="green"
          isLoading={$isLoading.financial}
          format="currency"
          subtitle="所有项目补助总额"
        />

        <KpiCard
          title="平均项目补助"
          value={financialMetrics.averageSubsidyPerProject}
          icon={Target}
          color="blue"
          isLoading={$isLoading.financial}
          format="currency"
          subtitle="每个项目平均金额"
        />

        <KpiCard
          title="补助类型数"
          value={financialMetrics.subsidyByType?.length || 0}
          icon={BarChart3}
          color="purple"
          isLoading={$isLoading.financial}
          subtitle="不同补助类型数量"
        />

        <KpiCard
          title="资助项目数"
          value={financialMetrics.topSubsidizedProjects?.length || 0}
          icon={Building}
          color="indigo"
          isLoading={$isLoading.financial}
          subtitle="获得资助的项目"
        />
      </div>

      <!-- 财务图表 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <MetricCard
          title="补助类型分布"
          data={financialMetrics.subsidyByType}
          chartType="pie"
          isLoading={$isLoading.financial}
          error={$errors.financial}
          icon={DollarSign}
        />

        <MetricCard
          title="项目补助排行"
          data={financialMetrics.topSubsidizedProjects}
          chartType="bar"
          isLoading={$isLoading.financial}
          error={$errors.financial}
          icon={Target}
        />
      </div>
    {/if}

    <!-- 人员分析视图 -->
    {#if selectedView === 'personnel'}
      <!-- 人员 KPI -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <KpiCard
          title="总人员数"
          value={personnelMetrics.totalPersonnel}
          icon={Users}
          color="blue"
          isLoading={$isLoading.personnel}
          subtitle="参与项目的总人数"
        />

        <KpiCard
          title="PI 人数"
          value={personnelMetrics.piDistribution?.filter(p => p.isPI).length || 0}
          icon={UserCheck}
          color="green"
          isLoading={$isLoading.personnel}
          subtitle="主要研究者数量"
        />

        <KpiCard
          title="角色类型数"
          value={personnelMetrics.personnelByRole?.length || 0}
          icon={Target}
          color="purple"
          isLoading={$isLoading.personnel}
          subtitle="不同角色类型"
        />

        <KpiCard
          title="平均利用率"
          value={85}
          icon={BarChart3}
          color="indigo"
          isLoading={$isLoading.personnel}
          format="percentage"
          subtitle="人员参与项目比率"
        />
      </div>

      <!-- 人员图表 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <MetricCard
          title="人员角色分布"
          data={personnelMetrics.personnelByRole}
          chartType="pie"
          isLoading={$isLoading.personnel}
          error={$errors.personnel}
          icon={Users}
        />

        <MetricCard
          title="人员工作负荷"
          data={personnelMetrics.personnelUtilization}
          chartType="bar"
          isLoading={$isLoading.personnel}
          error={$errors.personnel}
          icon={BarChart3}
        />
      </div>
    {/if}

    <!-- 时间线分析视图 -->
    {#if selectedView === 'timeline'}
      <!-- 时间线 KPI -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <KpiCard
          title="平均项目周期"
          value={timelineMetrics.averageProjectDuration}
          icon={Clock}
          color="blue"
          isLoading={$isLoading.timeline}
          format="duration"
          subtitle="项目平均持续时间"
        />

        <KpiCard
          title="即将到期"
          value={timelineMetrics.upcomingDeadlines?.length || 0}
          icon={AlertCircle}
          color="red"
          isLoading={$isLoading.timeline}
          subtitle="30天内到期项目"
        />

        <KpiCard
          title="阶段转换"
          value={timelineMetrics.projectPhaseTransitions?.length || 0}
          icon={TrendingUp}
          color="green"
          isLoading={$isLoading.timeline}
          subtitle="本月阶段变更"
        />

        <KpiCard
          title="启动项目数"
          value={timelineMetrics.projectsByStartMonth?.reduce((sum, month) => sum + month.value, 0) || 0}
          icon={Calendar}
          color="purple"
          isLoading={$isLoading.timeline}
          subtitle="近12个月启动"
        />
      </div>

      <!-- 时间线图表 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <MetricCard
          title="项目启动时间分布"
          data={timelineMetrics.projectsByStartMonth}
          chartType="line"
          isLoading={$isLoading.timeline}
          error={$errors.timeline}
          icon={TrendingUp}
        />

        <MetricCard
          title="即将到期项目"
          data={timelineMetrics.upcomingDeadlines}
          chartType="table"
          isLoading={$isLoading.timeline}
          error={$errors.timeline}
          icon={AlertCircle}
        />
      </div>
    {/if}

    <!-- 详细数据表格区域 -->
    <div class="space-y-8">
      <!-- 其他详细数据表格可以在这里添加 -->
    </div>
  </div>
</div>
