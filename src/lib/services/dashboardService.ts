import { invoke } from '@tauri-apps/api/core';

// SQLite 数据库路径
const DB_PATH = '/Users/<USER>/我的文档/sqlite/peckbyte.db';

// 图表数据点
export interface ChartDataPoint {
  name: string;
  value: number;
  series?: string;
}

// 项目状态分布
export interface ProjectStatusDistribution {
  status_name: string;
  project_count: number;
}

// 仪表盘概览数据
export interface DashboardOverview {
  active_project_count: number;
  total_project_count: number;
  recruiting_project_count: number;
}

// 日期范围参数
export interface DateRangeParams {
  start_date?: string;
  end_date?: string;
}

// 项目状态筛选参数
export interface ProjectStatusFilterParams {
  status_ids?: number[];
}

// 申办方筛选参数
export interface SponsorFilterParams {
  sponsor_ids?: number[];
}

// 疾病领域筛选参数
export interface DiseaseFilterParams {
  disease_ids?: number[];
}

// 财务指标数据
export interface FinancialMetrics {
  totalSubsidyAmount: number;
  averageSubsidyPerProject: number;
  subsidyByType: ChartDataPoint[];
  topSubsidizedProjects: any[];
}

// 人员指标数据
export interface PersonnelMetrics {
  totalPersonnel: number;
  personnelByRole: ChartDataPoint[];
  personnelUtilization: any[];
  piDistribution: any[];
}

// 时间线指标数据
export interface TimelineMetrics {
  projectsByStartMonth: ChartDataPoint[];
  averageProjectDuration: number;
  upcomingDeadlines: any[];
  projectPhaseTransitions: any[];
}

// 项目阶段筛选参数
export interface ProjectStageFilterParams {
  stage_ids?: number[];
}

// 招募状态筛选参数
export interface RecruitmentStatusFilterParams {
  recruitment_ids?: number[];
}

// 仪表盘筛选参数（前端使用的嵌套结构）
export interface DashboardFilterParams {
  date_range?: DateRangeParams;
  project_status?: ProjectStatusFilterParams;
  project_stage?: ProjectStageFilterParams;
  recruitment_status?: RecruitmentStatusFilterParams;
  sponsor?: SponsorFilterParams;
  disease?: DiseaseFilterParams;
}

// 后端期望的扁平化筛选参数
export interface FlatDashboardFilterParams {
  start_date?: string;
  end_date?: string;
  status_ids?: number[];
  stage_ids?: number[];
  recruitment_ids?: number[];
  sponsor_ids?: number[];
  disease_ids?: number[];
}

// 将前端嵌套参数转换为后端期望的扁平化参数
function flattenFilterParams(filters?: DashboardFilterParams): FlatDashboardFilterParams {
  if (!filters) return {};
  
  const flattened: FlatDashboardFilterParams = {};
  
  if (filters.date_range?.start_date) {
    flattened.start_date = filters.date_range.start_date;
  }
  if (filters.date_range?.end_date) {
    flattened.end_date = filters.date_range.end_date;
  }
  if (filters.project_status?.status_ids) {
    flattened.status_ids = filters.project_status.status_ids;
  }
  if (filters.project_stage?.stage_ids) {
    flattened.stage_ids = filters.project_stage.stage_ids;
  }
  if (filters.recruitment_status?.recruitment_ids) {
    flattened.recruitment_ids = filters.recruitment_status.recruitment_ids;
  }
  if (filters.sponsor?.sponsor_ids) {
    flattened.sponsor_ids = filters.sponsor.sponsor_ids;
  }
  if (filters.disease?.disease_ids) {
    flattened.disease_ids = filters.disease.disease_ids;
  }
  
  return flattened;
}

// 仪表盘服务
export const dashboardService = {
  // 获取仪表盘概览数据
  async getDashboardOverview(filterParams?: DashboardFilterParams): Promise<DashboardOverview> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      console.log('发送仪表盘概览数据请求，扁平化参数:', flatParams);
      
      return await invoke('get_dashboard_overview', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取仪表盘概览数据失败:', error);
      throw error;
    }
  },

  // 获取项目状态分布
  async getProjectStatusDistribution(filterParams?: DashboardFilterParams): Promise<ProjectStatusDistribution[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      console.log('发送项目状态分布请求，扁平化参数:', flatParams);
      
      return await invoke('get_project_status_distribution', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取项目状态分布失败:', error);
      throw error;
    }
  },

  // 获取项目状态分布图表数据
  async getProjectStatusChartData(filterParams?: DashboardFilterParams): Promise<ChartDataPoint[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_project_status_chart_data', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取项目状态分布图表数据失败:', error);
      throw error;
    }
  },

  // 获取项目阶段分布
  async getProjectStageDistribution(filterParams?: DashboardFilterParams): Promise<any[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_project_stage_distribution', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取项目阶段分布失败:', error);
      throw error;
    }
  },

  // 获取项目阶段分布图表数据
  async getProjectStageChartData(filterParams?: DashboardFilterParams): Promise<ChartDataPoint[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_project_stage_chart_data', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取项目阶段分布图表数据失败:', error);
      throw error;
    }
  },

  // 获取招募状态分布
  async getRecruitmentStatusDistribution(filterParams?: DashboardFilterParams): Promise<any[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_recruitment_status_distribution', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取招募状态分布失败:', error);
      throw error;
    }
  },

  // 获取招募状态分布图表数据
  async getRecruitmentStatusChartData(filterParams?: DashboardFilterParams): Promise<ChartDataPoint[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_recruitment_status_chart_data', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取招募状态分布图表数据失败:', error);
      throw error;
    }
  },

  // 获取疾病领域分布
  async getDiseaseDistribution(filterParams?: DashboardFilterParams): Promise<any[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_disease_distribution', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取疾病领域分布失败:', error);
      throw error;
    }
  },

  // 获取疾病领域分布图表数据
  async getDiseaseChartData(filterParams?: DashboardFilterParams): Promise<ChartDataPoint[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_disease_chart_data', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取疾病领域分布图表数据失败:', error);
      throw error;
    }
  },

  // 获取申办方项目分布
  async getSponsorDistribution(filterParams?: DashboardFilterParams): Promise<any[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_sponsor_distribution', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取申办方项目分布失败:', error);
      throw error;
    }
  },

  // 获取申办方项目分布图表数据
  async getSponsorChartData(filterParams?: DashboardFilterParams): Promise<ChartDataPoint[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_sponsor_chart_data', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取申办方项目分布图表数据失败:', error);
      throw error;
    }
  },

  // 获取每月新启动项目数
  async getMonthlyNewProjects(filterParams?: DashboardFilterParams): Promise<any[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_monthly_new_projects', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取每月新启动项目数失败:', error);
      throw error;
    }
  },

  // 获取每月新启动项目数图表数据
  async getMonthlyNewProjectsChartData(filterParams?: DashboardFilterParams): Promise<ChartDataPoint[]> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_monthly_new_projects_chart_data', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取每月新启动项目数图表数据失败:', error);
      throw error;
    }
  },

  // 调试：检查项目数据统计
  async debugProjectDateStatistics(): Promise<string> {
    try {
      return await invoke('debug_project_date_statistics', {
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('调试项目数据统计失败:', error);
      throw error;
    }
  },





  // 获取财务指标数据
  async getFinancialMetrics(filterParams?: DashboardFilterParams): Promise<FinancialMetrics> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_financial_metrics', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取财务指标数据失败:', error);
      throw error;
    }
  },

  // 获取人员指标数据
  async getPersonnelMetrics(filterParams?: DashboardFilterParams): Promise<PersonnelMetrics> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_personnel_metrics', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取人员指标数据失败:', error);
      throw error;
    }
  },

  // 获取时间线指标数据
  async getTimelineMetrics(filterParams?: DashboardFilterParams): Promise<TimelineMetrics> {
    try {
      const flatParams = flattenFilterParams(filterParams);
      
      return await invoke('get_timeline_metrics', {
        filterParams: flatParams,
        dbPath: DB_PATH
      });
    } catch (error) {
      console.error('获取时间线指标数据失败:', error);
      throw error;
    }
  }
};
