<script lang="ts">
  import * as Dialog from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import { FileDown, FileText, FileJson } from 'lucide-svelte';

  // 组件属性
  let { open = $bindable(false), projects = $bindable([]) } = $props();

  // 导出配置
  let exportFormat = $state('csv'); // 'csv' or 'json'

  // 执行导出
  function executeExport() {
    if (!projects || projects.length === 0) {
      alert('没有可导出的数据');
      return;
    }

    // 准备导出数据
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `项目列表_${timestamp}.${exportFormat}`;

    // 导出数据
    if (exportFormat === 'csv') {
      exportToCSV(fileName);
    } else {
      exportToJSON(fileName);
    }
  }

  // 导出为CSV
  function exportToCSV(fileName: string) {
    // 准备CSV标题行
    const headers = [
      '项目ID',
      '项目全称',
      '项目简称',
      '疾病',
      '研究分期',
      '项目状态',
      '招募状态',
      '启动日期'
    ];

    // 准备CSV数据行
    const rows = projects.map(project => [
      project.project.project_id || '',
      project.project.project_name || '',
      project.project.project_short_name || '',
      project.disease?.item_value || '',
      project.project_stage?.item_value || '',
      project.project_status?.item_value || '',
      project.recruitment_status?.item_value || '',
      project.project.project_start_date || ''
    ]);

    // 组合CSV内容
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${(cell || '').toString().replace(/"/g, '""')}"`).join(','))
    ].join('\n');

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    // 设置下载属性
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';

    // 添加到文档并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  // 导出为JSON
  function exportToJSON(fileName: string) {
    // 准备JSON数据
    const jsonData = projects.map(project => ({
      project_id: project.project.project_id,
      project_name: project.project.project_name,
      project_short_name: project.project.project_short_name,
      disease: project.disease?.item_value,
      project_stage: project.project_stage?.item_value,
      project_status: project.project_status?.item_value,
      recruitment_status: project.recruitment_status?.item_value,
      project_start_date: project.project.project_start_date
    }));

    // 添加元数据
    const exportData = {
      metadata: {
        export_date: new Date().toISOString(),
        export_version: '1.0.0',
        total_projects: jsonData.length
      },
      projects: jsonData
    };

    // 创建Blob对象
    const jsonContent = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    // 设置下载属性
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';

    // 添加到文档并触发点击
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="max-w-md">
    <Dialog.Header>
      <Dialog.Title class="text-xl">导出项目数据</Dialog.Title>
      <Dialog.Description>
        选择导出格式并下载项目数据
      </Dialog.Description>
    </Dialog.Header>

    <div class="py-4">
      <div class="grid grid-cols-2 gap-4">
        <div
          role="button"
          tabindex="0"
          class="border rounded-lg p-4 flex flex-col items-center gap-2 cursor-pointer hover:bg-gray-50 transition-colors {exportFormat === 'csv' ? 'border-blue-500 bg-blue-50' : ''}"
          onclick={() => exportFormat = 'csv'}
          onkeydown={(e) => e.key === 'Enter' && (exportFormat = 'csv')}
          aria-label="选择CSV格式"
        >
          <FileText class="h-12 w-12 text-blue-500" />
          <h3 class="font-medium">CSV 格式</h3>
          <p class="text-sm text-gray-500 text-center">导出为逗号分隔值文件，可在Excel等电子表格软件中打开</p>
        </div>

        <div
          role="button"
          tabindex="0"
          class="border rounded-lg p-4 flex flex-col items-center gap-2 cursor-pointer hover:bg-gray-50 transition-colors {exportFormat === 'json' ? 'border-blue-500 bg-blue-50' : ''}"
          onclick={() => exportFormat = 'json'}
          onkeydown={(e) => e.key === 'Enter' && (exportFormat = 'json')}
          aria-label="选择JSON格式"
        >
          <FileJson class="h-12 w-12 text-green-500" />
          <h3 class="font-medium">JSON 格式</h3>
          <p class="text-sm text-gray-500 text-center">导出为结构化JSON数据，包含完整的项目信息</p>
        </div>
      </div>
    </div>

    <Dialog.Footer>
      <div class="flex justify-between w-full">
        <Button variant="outline" onclick={() => open = false}>
          取消
        </Button>
        <Button onclick={executeExport} class="gap-2">
          <FileDown class="h-4 w-4" />
          导出数据
        </Button>
      </div>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>
