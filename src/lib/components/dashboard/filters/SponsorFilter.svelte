<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';

  // 属性
  const { selectedSponsorIds = $bindable([]), label = '申办方' } = $props();

  // 状态
  let sponsorOptions = $state<{ item_id: number; item_value: string }[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let searchTerm = $state('');
  let filteredOptions = $state<typeof sponsorOptions>([]);

  // 事件处理
  const dispatch = createEventDispatcher();

  // 加载申办方选项
  async function loadSponsorOptions() {
    try {
      isLoading = true;
      error = null;

      // 获取申办方字典
      const dictionaries = await sqliteDictionaryService.getAllDicts();
      const sponsorDict = dictionaries.find((dict: any) => dict.name === '申办方');

      if (sponsorDict) {
        const items = await sqliteDictionaryService.getDictItems(sponsorDict.id || 0);
        sponsorOptions = items.map((item: any) => ({
          item_id: item.item_id,
          item_value: item.value
        }));
        filteredOptions = [...sponsorOptions];
      } else {
        error = '未找到申办方字典';
      }
    } catch (err: any) {
      error = err.message || '加载申办方选项失败';
      console.error('加载申办方选项失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 处理申办方变更
  function handleSponsorChange(sponsorId: number, checked: boolean) {
    if (checked) {
      // 使用新的数组，保持原数组不变
      const newIds = [...selectedSponsorIds, sponsorId];
      selectedSponsorIds.set(newIds);
    } else {
      // 过滤掉取消选中的ID
      const newIds = selectedSponsorIds.filter(id => id !== sponsorId);
      selectedSponsorIds.set(newIds);
    }

    dispatch('change', {
      sponsor_ids: selectedSponsorIds.length > 0 ? [...selectedSponsorIds] : undefined
    });
  }

  // 清除申办方筛选
  function clearSponsorFilter() {
    selectedSponsorIds.set([]);
    dispatch('change', { sponsor_ids: undefined });
  }

  // 筛选申办方选项
  $effect(() => {
    if (searchTerm.trim() === '') {
      filteredOptions = sponsorOptions;
    } else {
      const term = searchTerm.toLowerCase();
      filteredOptions = sponsorOptions.filter(sponsor =>
        sponsor.item_value.toLowerCase().includes(term)
      );
    }
  });

  // 组件挂载时加载申办方选项
  onMount(() => {
    loadSponsorOptions();
  });
</script>

<div class="sponsor-filter">
  <div class="mb-2">
    <div class="flex justify-between items-center">
      <div class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </div>
      {#if selectedSponsorIds.length > 0}
        <button
          type="button"
          onclick={() => clearSponsorFilter()}
          class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          清除
        </button>
      {/if}
    </div>

    {#if isLoading}
      <div class="mt-2 flex items-center">
        <div class="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
        <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">加载中...</span>
      </div>
    {:else if error}
      <div class="mt-2 text-sm text-red-500 dark:text-red-400">{error}</div>
    {:else}
      <div class="mt-2">
        <input
          type="text"
          placeholder="搜索申办方..."
          bind:value={searchTerm}
          class="block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm mb-2"
        />

        <div class="max-h-40 overflow-y-auto space-y-2 border border-gray-200 dark:border-gray-700 rounded-md p-2">
          {#if filteredOptions.length === 0}
            <div class="text-sm text-gray-500 dark:text-gray-400 text-center py-2">
              {searchTerm ? '未找到匹配的申办方' : '暂无申办方数据'}
            </div>
          {:else}
            {#each filteredOptions as sponsor}
              <div class="flex items-center">
                <input
                  type="checkbox"
                  id="sponsor-{sponsor.item_id}"
                  checked={selectedSponsorIds.includes(sponsor.item_id)}
                  onchange={(e) => handleSponsorChange(sponsor.item_id, e.currentTarget.checked)}
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
                />
                <label for="sponsor-{sponsor.item_id}" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  {sponsor.item_value}
                </label>
              </div>
            {/each}
          {/if}
        </div>
      </div>
    {/if}
  </div>
</div>
