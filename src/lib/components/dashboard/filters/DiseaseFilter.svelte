<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { mcp_sqlite_explorer_read_query } from '$lib/services/sqliteExplorerService';

  // 属性
  const { label = '疾病领域' } = $props();
  let selectedDiseaseIds = $state<number[]>([]);

  // 状态
  let diseaseOptions = $state<{ item_id: number; item_value: string }[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);

  // 事件处理
  const dispatch = createEventDispatcher<{change: {disease_ids?: number[]};}>();

  // 加载疾病领域选项
  async function loadDiseaseOptions() {
    try {
      isLoading = true;
      error = null;

      // 获取疾病字典项（字典ID: 5）
      const results = await mcp_sqlite_explorer_read_query({
        query: `SELECT item_id, item_value 
                FROM dictionary_items 
                WHERE dictionary_id = 5 AND status = 'active'
                ORDER BY item_value`,
        fetch_all: true
      });

      diseaseOptions = results.map((item: any) => ({
        item_id: item.item_id,
        item_value: item.item_value
      }));

      // 默认全部选中
      selectedDiseaseIds = diseaseOptions.map(option => option.item_id);
      
      // 触发初始变更事件
      dispatch('change', {
        disease_ids: selectedDiseaseIds.length > 0 ? [...selectedDiseaseIds] : undefined
      });

    } catch (err: any) {
      error = err.message || '加载疾病领域选项失败';
      console.error('加载疾病领域选项失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 处理疾病变更
  function handleDiseaseChange(diseaseId: number, checked: boolean) {
    if (checked) {
      selectedDiseaseIds = [...selectedDiseaseIds, diseaseId];
    } else {
      selectedDiseaseIds = selectedDiseaseIds.filter((id: number) => id !== diseaseId);
    }

    dispatch('change', {
      disease_ids: selectedDiseaseIds.length > 0 ? [...selectedDiseaseIds] : undefined
    });
  }

  // 全选功能
  function selectAll() {
    selectedDiseaseIds = [...diseaseOptions.map(option => option.item_id)];
    dispatch('change', {
      disease_ids: selectedDiseaseIds.length > 0 ? [...selectedDiseaseIds] : undefined
    });
  }

  // 清除疾病筛选
  function clearDiseaseFilter() {
    selectedDiseaseIds = [];
    dispatch('change', { disease_ids: undefined });
  }

  // 组件挂载时加载疾病选项
  onMount(() => {
    loadDiseaseOptions();
  });
</script>

<div class="disease-filter">
  <div class="mb-2">
    <div class="flex justify-between items-center">
      <div class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </div>
      <div class="flex space-x-2">
        {#if selectedDiseaseIds.length === 0}
          <button
            type="button"
            onclick={() => selectAll()}
            class="text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
          >
            全选
          </button>
        {/if}
        {#if selectedDiseaseIds.length > 0 && selectedDiseaseIds.length < diseaseOptions.length}
          <button
            type="button"
            onclick={() => clearDiseaseFilter()}
            class="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
          >
            清除
          </button>
        {/if}
      </div>
    </div>

    {#if selectedDiseaseIds.length > 0}
      <div class="mt-1 text-xs text-blue-600 dark:text-blue-400">
        已选择 {selectedDiseaseIds.length}/{diseaseOptions.length} 个领域
      </div>
    {/if}

    {#if isLoading}
      <div class="mt-2 flex items-center">
        <div class="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
        <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">加载中...</span>
      </div>
    {:else if error}
      <div class="mt-2 text-sm text-red-500 dark:text-red-400">{error}</div>
    {:else}
      <div class="mt-2 max-h-40 overflow-y-auto space-y-2">
        {#each diseaseOptions as disease}
          <div class="flex items-center">
            <input
              type="checkbox"
              id="disease-{disease.item_id}"
              checked={selectedDiseaseIds.includes(disease.item_id)}
              onchange={(e) => handleDiseaseChange(disease.item_id, e.currentTarget.checked)}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
            />
            <label for="disease-{disease.item_id}" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              {disease.item_value}
            </label>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div>
