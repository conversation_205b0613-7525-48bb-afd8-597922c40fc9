<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { mcp_sqlite_explorer_read_query } from '$lib/services/sqliteExplorerService';

  // 属性
  const { label = '招募状态' } = $props();
  let selectedRecruitmentIds = $state<number[]>([]);

  // 状态
  let recruitmentOptions = $state<{ item_id: number; item_value: string }[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);

  // 事件处理
  const dispatch = createEventDispatcher<{change: {recruitment_ids?: number[]};}>();

  // 加载招募状态选项
  async function loadRecruitmentOptions() {
    try {
      isLoading = true;
      error = null;

      // 获取招募状态字典项（字典ID: 10）
      const results = await mcp_sqlite_explorer_read_query({
        query: `SELECT item_id, item_value 
                FROM dictionary_items 
                WHERE dictionary_id = 10 AND status = 'active'
                ORDER BY item_id`,
        fetch_all: true
      });

      recruitmentOptions = results.map((item: any) => ({
        item_id: item.item_id,
        item_value: item.item_value
      }));

      // 默认全部选中
      selectedRecruitmentIds = recruitmentOptions.map(option => option.item_id);
      
      // 触发初始变更事件
      dispatch('change', {
        recruitment_ids: selectedRecruitmentIds.length > 0 ? [...selectedRecruitmentIds] : undefined
      });

    } catch (err: any) {
      error = err.message || '加载招募状态选项失败';
      console.error('加载招募状态选项失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 处理招募状态变更
  function handleRecruitmentChange(recruitmentId: number, checked: boolean) {
    if (checked) {
      selectedRecruitmentIds = [...selectedRecruitmentIds, recruitmentId];
    } else {
      selectedRecruitmentIds = selectedRecruitmentIds.filter((id: number) => id !== recruitmentId);
    }

    dispatch('change', {
      recruitment_ids: selectedRecruitmentIds.length > 0 ? [...selectedRecruitmentIds] : undefined
    });
  }

  // 全选功能
  function selectAll() {
    selectedRecruitmentIds = [...recruitmentOptions.map(option => option.item_id)];
    dispatch('change', {
      recruitment_ids: selectedRecruitmentIds.length > 0 ? [...selectedRecruitmentIds] : undefined
    });
  }

  // 清除招募状态筛选
  function clearRecruitmentFilter() {
    selectedRecruitmentIds = [];
    dispatch('change', { recruitment_ids: undefined });
  }

  // 组件挂载时加载招募状态选项
  onMount(() => {
    loadRecruitmentOptions();
  });
</script>

<div class="recruitment-status-filter">
  <div class="mb-2">
    <div class="flex justify-between items-center">
      <div class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </div>
      <div class="flex space-x-2">
        {#if selectedRecruitmentIds.length === 0}
          <button
            type="button"
            onclick={() => selectAll()}
            class="text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
          >
            全选
          </button>
        {/if}
        {#if selectedRecruitmentIds.length > 0 && selectedRecruitmentIds.length < recruitmentOptions.length}
          <button
            type="button"
            onclick={() => clearRecruitmentFilter()}
            class="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
          >
            清除
          </button>
        {/if}
      </div>
    </div>

    {#if selectedRecruitmentIds.length > 0}
      <div class="mt-1 text-xs text-blue-600 dark:text-blue-400">
        已选择 {selectedRecruitmentIds.length}/{recruitmentOptions.length} 个状态
      </div>
    {/if}

    {#if isLoading}
      <div class="mt-2 flex items-center">
        <div class="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
        <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">加载中...</span>
      </div>
    {:else if error}
      <div class="mt-2 text-sm text-red-500 dark:text-red-400">{error}</div>
    {:else}
      <div class="mt-2 space-y-2">
        {#each recruitmentOptions as recruitment}
          <div class="flex items-center">
            <input
              type="checkbox"
              id="recruitment-{recruitment.item_id}"
              checked={selectedRecruitmentIds.includes(recruitment.item_id)}
              onchange={(e) => handleRecruitmentChange(recruitment.item_id, e.currentTarget.checked)}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
            />
            <label for="recruitment-{recruitment.item_id}" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              {recruitment.item_value}
            </label>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div> 