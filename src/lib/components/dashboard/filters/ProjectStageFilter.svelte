<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import { mcp_sqlite_explorer_read_query } from '$lib/services/sqliteExplorerService';

  // 属性
  const { label = '项目阶段' } = $props();
  let selectedStageIds = $state<number[]>([]);

  // 状态
  let stageOptions = $state<{ item_id: number; item_value: string }[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);

  // 事件处理
  const dispatch = createEventDispatcher<{change: {stage_ids?: number[]};}>();

  // 加载项目阶段选项
  async function loadStageOptions() {
    try {
      isLoading = true;
      error = null;

      // 获取研究分期字典项（字典ID: 7）
      const results = await mcp_sqlite_explorer_read_query({
        query: `SELECT item_id, item_value 
                FROM dictionary_items 
                WHERE dictionary_id = 7 AND status = 'active'
                ORDER BY item_id`,
        fetch_all: true
      });

      stageOptions = results.map((item: any) => ({
        item_id: item.item_id,
        item_value: item.item_value
      }));

      // 默认全部选中
      selectedStageIds = stageOptions.map(option => option.item_id);
      
      // 触发初始变更事件
      dispatch('change', {
        stage_ids: selectedStageIds.length > 0 ? [...selectedStageIds] : undefined
      });

    } catch (err: any) {
      error = err.message || '加载项目阶段选项失败';
      console.error('加载项目阶段选项失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 处理阶段变更
  function handleStageChange(stageId: number, checked: boolean) {
    if (checked) {
      selectedStageIds = [...selectedStageIds, stageId];
    } else {
      selectedStageIds = selectedStageIds.filter((id: number) => id !== stageId);
    }

    dispatch('change', {
      stage_ids: selectedStageIds.length > 0 ? [...selectedStageIds] : undefined
    });
  }

  // 全选功能
  function selectAll() {
    selectedStageIds = [...stageOptions.map(option => option.item_id)];
    dispatch('change', {
      stage_ids: selectedStageIds.length > 0 ? [...selectedStageIds] : undefined
    });
  }

  // 清除阶段筛选
  function clearStageFilter() {
    selectedStageIds = [];
    dispatch('change', { stage_ids: undefined });
  }

  // 组件挂载时加载阶段选项
  onMount(() => {
    loadStageOptions();
  });
</script>

<div class="project-stage-filter">
  <div class="mb-2">
    <div class="flex justify-between items-center">
      <div class="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </div>
      <div class="flex space-x-2">
        {#if selectedStageIds.length === 0}
          <button
            type="button"
            onclick={() => selectAll()}
            class="text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
          >
            全选
          </button>
        {/if}
        {#if selectedStageIds.length > 0 && selectedStageIds.length < stageOptions.length}
          <button
            type="button"
            onclick={() => clearStageFilter()}
            class="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
          >
            清除
          </button>
        {/if}
      </div>
    </div>

    {#if selectedStageIds.length > 0}
      <div class="mt-1 text-xs text-blue-600 dark:text-blue-400">
        已选择 {selectedStageIds.length}/{stageOptions.length} 个阶段
      </div>
    {/if}

    {#if isLoading}
      <div class="mt-2 flex items-center">
        <div class="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
        <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">加载中...</span>
      </div>
    {:else if error}
      <div class="mt-2 text-sm text-red-500 dark:text-red-400">{error}</div>
    {:else}
      <div class="mt-2 max-h-40 overflow-y-auto space-y-2">
        {#each stageOptions as stage}
          <div class="flex items-center">
            <input
              type="checkbox"
              id="stage-{stage.item_id}"
              checked={selectedStageIds.includes(stage.item_id)}
              onchange={(e) => handleStageChange(stage.item_id, e.currentTarget.checked)}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
            />
            <label for="stage-{stage.item_id}" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              {stage.item_value}
            </label>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div> 